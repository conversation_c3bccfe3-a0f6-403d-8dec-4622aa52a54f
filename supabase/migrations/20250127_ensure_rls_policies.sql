-- Ensure RLS policies exist for all tables used by the posting preferences edge function

-- Enable RLS on post_preferences table if not already enabled
ALTER TABLE public.post_preferences ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for post_preferences table
DO $$ 
BEGIN
  -- Check if policy exists before creating
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'post_preferences' 
    AND policyname = 'Users can view their own post preferences'
  ) THEN
    CREATE POLICY "Users can view their own post preferences" 
      ON public.post_preferences 
      FOR SELECT 
      USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'post_preferences' 
    AND policyname = 'Users can insert their own post preferences'
  ) THEN
    CREATE POLICY "Users can insert their own post preferences" 
      ON public.post_preferences 
      FOR INSERT 
      WITH CHECK (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'post_preferences' 
    AND policyname = 'Users can update their own post preferences'
  ) THEN
    CREATE POLICY "Users can update their own post preferences" 
      ON public.post_preferences 
      FOR UPDATE 
      USING (auth.uid() = user_id);
  END IF;
END $$;

-- Enable RLS on rss_feeds table if not already enabled
ALTER TABLE public.rss_feeds ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for rss_feeds table
DO $$ 
BEGIN
  -- Check if policy exists before creating
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'rss_feeds' 
    AND policyname = 'Users can view their own rss feeds'
  ) THEN
    CREATE POLICY "Users can view their own rss feeds" 
      ON public.rss_feeds 
      FOR SELECT 
      USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'rss_feeds' 
    AND policyname = 'Users can insert their own rss feeds'
  ) THEN
    CREATE POLICY "Users can insert their own rss feeds" 
      ON public.rss_feeds 
      FOR INSERT 
      WITH CHECK (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'rss_feeds' 
    AND policyname = 'Users can update their own rss feeds'
  ) THEN
    CREATE POLICY "Users can update their own rss feeds" 
      ON public.rss_feeds 
      FOR UPDATE 
      USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'rss_feeds' 
    AND policyname = 'Users can delete their own rss feeds'
  ) THEN
    CREATE POLICY "Users can delete their own rss feeds" 
      ON public.rss_feeds 
      FOR DELETE 
      USING (auth.uid() = user_id);
  END IF;
END $$;

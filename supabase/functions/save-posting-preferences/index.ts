import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.4';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface PostingPreferencesRequest {
  frequency: number;
  daysOfWeek: string[];
  timeOfDay: string;
  topics: string[];
  sentiment: string;
  platforms: string[];
  contentMode: string;
  rssFeeds: string[];
  language: string;
  makeComWebhookUrl?: string;
  selectedLinkedInOrganization?: {
    id: string;
    organization_id: string;
    organization_name: string;
    organization_type: 'person' | 'org';
    vanity_name: string | null;
    logo_url: string | null;
  };
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? ''
    );

    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Set the auth context
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid or expired token' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const requestData: PostingPreferencesRequest = await req.json();
    
    // Validate required fields
    const validationErrors: string[] = [];
    
    if (!requestData.frequency || requestData.frequency < 1 || requestData.frequency > 10) {
      validationErrors.push('Frequency must be between 1 and 10 posts per day');
    }
    
    if (!requestData.daysOfWeek || requestData.daysOfWeek.length === 0) {
      validationErrors.push('At least one day of the week must be selected');
    }
    
    if (!requestData.timeOfDay || !/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(requestData.timeOfDay)) {
      validationErrors.push('Valid time of day is required (HH:MM format)');
    }
    
    if (!requestData.sentiment) {
      validationErrors.push('Content sentiment is required');
    }
    
    if (!requestData.contentMode) {
      validationErrors.push('Content mode is required');
    }
    
    if (requestData.contentMode === 'rss_with_ai' && (!requestData.rssFeeds || requestData.rssFeeds.length === 0)) {
      validationErrors.push('RSS feed topic is required when using RSS with AI mode');
    }
    
    if (!requestData.platforms || requestData.platforms.length === 0) {
      validationErrors.push('At least one platform must be selected');
    }

    if (validationErrors.length > 0) {
      return new Response(
        JSON.stringify({ 
          error: 'Validation failed', 
          details: validationErrors 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    console.log('Saving posting preferences for user:', user.id);
    console.log('Request data:', requestData);

    // Start a transaction-like operation
    const operations = [];

    // 1. Save main posting preferences
    const { error: prefsError } = await supabase
      .from('post_preferences')
      .upsert({
        user_id: user.id,
        frequency: requestData.frequency,
        days_of_week: requestData.daysOfWeek,
        time_of_day: requestData.timeOfDay,
        topics: requestData.topics || [],
        sentiment: requestData.sentiment,
        platform: requestData.platforms,
        content_mode: requestData.contentMode,
        language: requestData.language || 'en',
        updated_at: new Date().toISOString()
      }, { onConflict: 'user_id' });

    if (prefsError) {
      console.error('Error saving preferences:', prefsError);
      throw new Error(`Failed to save preferences: ${prefsError.message}`);
    }

    operations.push('preferences');

    // 2. Handle RSS feeds if in RSS mode
    if (requestData.contentMode === 'rss_with_ai' && requestData.rssFeeds && requestData.rssFeeds.length > 0) {
      // First delete existing RSS feeds for this user
      const { error: deleteError } = await supabase
        .from('rss_feeds')
        .delete()
        .eq('user_id', user.id);

      if (deleteError) {
        console.error('Error deleting existing RSS feeds:', deleteError);
        throw new Error(`Failed to update RSS feeds: ${deleteError.message}`);
      }

      // Then insert new RSS feeds
      const feedsToInsert = requestData.rssFeeds.map(url => ({
        user_id: user.id,
        url: url.trim(),
        title: null, // Title will be fetched later
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

      const { error: rssError } = await supabase
        .from('rss_feeds')
        .insert(feedsToInsert);

      if (rssError) {
        console.error('Error saving RSS feeds:', rssError);
        throw new Error(`Failed to save RSS feeds: ${rssError.message}`);
      }

      operations.push('rss_feeds');
    }

    // 3. Save Make.com webhook URL and LinkedIn organization in AI agent preferences
    if (requestData.makeComWebhookUrl || requestData.selectedLinkedInOrganization) {
      const agentPreferences: any = {};
      
      if (requestData.makeComWebhookUrl) {
        agentPreferences.makeComWebhookUrl = requestData.makeComWebhookUrl.trim();
      }
      
      if (requestData.selectedLinkedInOrganization) {
        agentPreferences.selectedLinkedInOrganization = requestData.selectedLinkedInOrganization;
      }

      const { error: agentError } = await supabase
        .from('ai_agent_preferences')
        .upsert({
          user_id: user.id,
          agent_id: 'linkedin-thought-leader',
          preferences: agentPreferences,
          updated_at: new Date().toISOString()
        }, { onConflict: 'user_id,agent_id' });

      if (agentError) {
        console.error('Error saving agent preferences:', agentError);
        throw new Error(`Failed to save agent preferences: ${agentError.message}`);
      }

      operations.push('agent_preferences');
    }

    console.log('Successfully saved operations:', operations);

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Posting preferences saved successfully',
        operations: operations,
        timestamp: new Date().toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );

  } catch (error) {
    console.error('Error in save-posting-preferences function:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error.message || 'An unexpected error occurred'
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
})

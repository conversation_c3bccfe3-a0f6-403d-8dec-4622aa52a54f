import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.4';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Check required environment variables with robust validation
    const linkedinClientId = Deno.env.get('LINKEDIN_CLIENT_ID');
    const linkedinClientSecret = Deno.env.get('LINKEDIN_CLIENT_SECRET');
    const linkedinRedirectUri = Deno.env.get('LINKEDIN_EDGE_FUNCTION_REDIRECT_URI');
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

    // Robust check for missing environment variables
    if (!linkedinClientId || linkedinClientId.trim() === '') {
      console.error('[Edge Function] Missing required environment variable: LINKEDIN_CLIENT_ID');
      throw new Error('Server configuration error: LINKEDIN_CLIENT_ID is not configured');
    }
    
    if (!linkedinClientSecret || linkedinClientSecret.trim() === '') {
      console.error('[Edge Function] Missing required environment variable: LINKEDIN_CLIENT_SECRET');
      throw new Error('Server configuration error: LINKEDIN_CLIENT_SECRET is not configured');
    }
    
    if (!linkedinRedirectUri || linkedinRedirectUri.trim() === '') {
      console.error('[Edge Function] Missing required environment variable: LINKEDIN_EDGE_FUNCTION_REDIRECT_URI');
      throw new Error('Server configuration error: LINKEDIN_EDGE_FUNCTION_REDIRECT_URI is not configured');
    }
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('[Edge Function] Missing required Supabase environment variables');
      throw new Error('Server configuration error: Missing Supabase configuration');
    }

    console.log('[Edge Function] All required environment variables are configured');

    // Initialize Supabase client with service role key for database operations
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Parse request body
    const { code, userId } = await req.json();
    
    if (!code || !userId) {
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'Missing required parameters: code and userId' 
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    console.log('[Edge Function] Processing LinkedIn OAuth for user:', userId);
    console.log('[Edge Function] Using redirect URI:', linkedinRedirectUri);

    // Step 1: Exchange authorization code for access token
    const tokenResponse = await fetch('https://www.linkedin.com/oauth/v2/accessToken', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code: code,
        redirect_uri: linkedinRedirectUri,
        client_id: linkedinClientId,
        client_secret: linkedinClientSecret
      }).toString()
    });

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error('[Edge Function] LinkedIn Token Exchange Error:', errorText, 'Status:', tokenResponse.status);
      return new Response(
        JSON.stringify({ 
          success: false,
          error: `Failed to exchange code for token: ${errorText}` 
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    const tokenData = await tokenResponse.json();
    const accessToken = tokenData.access_token;
    const expiresIn = tokenData.expires_in;

    console.log('[Edge Function] Successfully obtained access token, expires in:', expiresIn, 'seconds');

    // Step 2: Fetch LinkedIn user info with proper headers
    const userInfoResponse = await fetch('https://api.linkedin.com/v2/userinfo', {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'X-Restli-Protocol-Version': '2.0.0'
      }
    });

    if (!userInfoResponse.ok) {
      const errorText = await userInfoResponse.text();
      console.error('[Edge Function] LinkedIn API Error - userinfo:', errorText, 'Status:', userInfoResponse.status);
      return new Response(
        JSON.stringify({ 
          success: false,
          error: `Failed to fetch user info: ${errorText}` 
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    const userInfo = await userInfoResponse.json();
    const linkedinUserId = userInfo.sub;
    const userName = userInfo.name || userInfo.given_name + ' ' + userInfo.family_name || 'LinkedIn User';
    const userEmail = userInfo.email;
    const userPicture = userInfo.picture;

    console.log('[Edge Function] LinkedIn user info:', { linkedinUserId, userName, userEmail });

    // Step 3: Store token in Supabase
    const expiresAt = expiresIn ? new Date(Date.now() + (expiresIn * 1000)).toISOString() : null;
    
    const { error: tokenError } = await supabase
      .from('linkedin_tokens')
      .upsert({
        user_id: userId,
        access_token: accessToken,
        linkedin_user_id: linkedinUserId,
        expires_at: expiresAt,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id'
      });

    if (tokenError) {
      console.error('[Edge Function] Error storing LinkedIn token:', tokenError);
      return new Response(
        JSON.stringify({ 
          success: false,
          error: `Failed to store access token: ${tokenError.message}` 
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    console.log('[Edge Function] Successfully stored LinkedIn token for user:', userId);

    // Step 4: Update user profile
    const { error: profileError } = await supabase
      .from('profiles')
      .update({ 
        linkedin_connected: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (profileError) {
      console.error('[Edge Function] Error updating profile:', profileError);
      // Don't fail the request for this, just log it
    }

    // Step 5: Save user profile with type "person"
    const { error: userProfileError } = await supabase
      .from('linkedin_organizations')
      .upsert({
        user_id: userId,
        organization_id: `urn:li:person:${linkedinUserId}`,
        organization_name: userName,
        organization_type: 'person',
        vanity_name: null,
        logo_url: userPicture || null,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id,organization_id'
      });

    if (userProfileError) {
      console.error('[Edge Function] Error storing user profile:', userProfileError);
      // Don't fail the request for this, just log it
    }

    // Step 6: Fetch managed organizations with proper headers
    let organizations = [];
    
    try {
      const orgsResponse = await fetch('https://api.linkedin.com/v2/organizationalEntityAcls?q=roleAssignee&role=ADMINISTRATOR&state=APPROVED', {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'X-Restli-Protocol-Version': '2.0.0'
        }
      });

      if (!orgsResponse.ok) {
        const errorText = await orgsResponse.text();
        console.error('[Edge Function] LinkedIn API Error - organizationalEntityAcls:', errorText, 'Status:', orgsResponse.status);
        // Continue without failing the entire request
      } else {
        const orgsData = await orgsResponse.json();
        console.log('[Edge Function] Organizations response:', orgsData);

        if (orgsData.elements && orgsData.elements.length > 0) {
          // Clear existing organizations for this user (excluding person type)
          await supabase
            .from('linkedin_organizations')
            .delete()
            .eq('user_id', userId)
            .eq('organization_type', 'org');

          // Fetch details for each organization
          const orgPromises = orgsData.elements.map(async (element: any) => {
            try {
              const orgUrn = element.organizationalTarget;
              const orgId = orgUrn.split(':').pop(); // Extract numeric ID from URN
              
              const orgDetailResponse = await fetch(`https://api.linkedin.com/v2/organizations/${orgId}`, {
                headers: {
                  'Authorization': `Bearer ${accessToken}`,
                  'X-Restli-Protocol-Version': '2.0.0'
                }
              });

              if (!orgDetailResponse.ok) {
                const errorText = await orgDetailResponse.text();
                console.error(`[Edge Function] LinkedIn API Error - organizations/${orgId}:`, errorText, 'Status:', orgDetailResponse.status);
                return null;
              }

              const orgDetail = await orgDetailResponse.json();
              return {
                user_id: userId,
                organization_id: orgUrn,
                organization_name: orgDetail.localizedName || `Organization ${orgId}`,
                organization_type: 'org',
                vanity_name: orgDetail.vanityName || null,
                logo_url: orgDetail.logoV2?.original || orgDetail.logoV2?.cropped?.com?.linkedin?.common?.VectorImage?.artifacts?.[0]?.fileIdentifyingUrlPathSegment || null,
                updated_at: new Date().toISOString()
              };
            } catch (error) {
              console.error('[Edge Function] Error fetching organization details:', error);
              return null;
            }
          });

          const orgDetails = (await Promise.all(orgPromises)).filter(Boolean);
          
          if (orgDetails.length > 0) {
            const { error: orgsError } = await supabase
              .from('linkedin_organizations')
              .insert(orgDetails);

            if (orgsError) {
              console.error('[Edge Function] Error storing organizations:', orgsError);
            } else {
              console.log('[Edge Function] Successfully stored', orgDetails.length, 'organizations');
              organizations = orgDetails.map(org => ({
                id: org.organization_id,
                name: org.organization_name,
                vanityName: org.vanity_name,
                logoUrl: org.logo_url
              }));
            }
          }
        }
      }
    } catch (error) {
      console.error('[Edge Function] Error in organization fetching process:', error);
      // Don't fail the entire request for organization fetching issues
    }

    // Step 7: Return success response
    return new Response(
      JSON.stringify({
        success: true,
        linkedinUserId,
        name: userName,
        email: userEmail,
        picture: userPicture,
        organizations
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );

  } catch (error) {
    console.error('[Edge Function] LinkedIn OAuth callback error:', error);
    return new Response(
      JSON.stringify({ 
        success: false,
        error: error.message || 'Internal server error' 
      }),
      {
      status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});

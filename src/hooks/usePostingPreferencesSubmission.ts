import { useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

export interface PostingPreferencesData {
  frequency: number;
  daysOfWeek: string[];
  timeOfDay: string;
  topics: string[];
  sentiment: string;
  platforms: string[];
  contentMode: string;
  rssFeeds: string[];
  language: string;
  makeComWebhookUrl?: string;
  selectedLinkedInOrganization?: {
    id: string;
    organization_id: string;
    organization_name: string;
    organization_type: 'person' | 'org';
    vanity_name: string | null;
    logo_url: string | null;
  };
}

export interface PostingPreferencesResponse {
  success: boolean;
  message: string;
  operations?: string[];
  timestamp?: string;
  error?: string;
  details?: string[];
}

export function usePostingPreferencesSubmission() {
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();

  const submitPreferences = async (data: PostingPreferencesData): Promise<PostingPreferencesResponse> => {
    if (!user) {
      const error = 'User must be authenticated to save preferences';
      toast({
        title: "Authentication Required",
        description: error,
        variant: "destructive",
      });
      return { success: false, error };
    }

    setLoading(true);

    try {
      // Get the current session to get the access token
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError || !session?.access_token) {
        throw new Error('Failed to get authentication session');
      }

      console.log('Submitting posting preferences:', data);

      // Call the edge function
      const response = await fetch(`${supabase.supabaseUrl}/functions/v1/save-posting-preferences`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify(data),
      });

      const result: PostingPreferencesResponse = await response.json();

      if (!response.ok) {
        throw new Error(result.error || `HTTP error! status: ${response.status}`);
      }

      if (!result.success) {
        throw new Error(result.error || 'Failed to save preferences');
      }

      console.log('Preferences saved successfully:', result);

      toast({
        title: "Preferences Saved",
        description: result.message || "Your posting preferences have been saved successfully.",
      });

      return result;

    } catch (error: any) {
      console.error('Error submitting posting preferences:', error);
      
      let errorMessage = 'Failed to save preferences. Please try again.';
      let errorDetails: string[] = [];

      if (error.message) {
        errorMessage = error.message;
      }

      // Handle validation errors
      if (error.details && Array.isArray(error.details)) {
        errorDetails = error.details;
        errorMessage = 'Please fix the following issues:';
      }

      toast({
        title: "Error Saving Preferences",
        description: errorDetails.length > 0 ? errorDetails.join(', ') : errorMessage,
        variant: "destructive",
      });

      return { 
        success: false, 
        error: errorMessage,
        details: errorDetails.length > 0 ? errorDetails : undefined
      };

    } finally {
      setLoading(false);
    }
  };

  const validatePreferences = (data: PostingPreferencesData): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    // Validate frequency
    if (!data.frequency || data.frequency < 1 || data.frequency > 10) {
      errors.push('Frequency must be between 1 and 10 posts per day');
    }

    // Validate days of week
    if (!data.daysOfWeek || data.daysOfWeek.length === 0) {
      errors.push('At least one day of the week must be selected');
    }

    // Validate time of day
    if (!data.timeOfDay || !/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(data.timeOfDay)) {
      errors.push('Valid time of day is required (HH:MM format)');
    }

    // Validate sentiment
    if (!data.sentiment) {
      errors.push('Content sentiment is required');
    }

    // Validate content mode
    if (!data.contentMode) {
      errors.push('Content mode is required');
    }

    // Validate RSS feeds for RSS mode
    if (data.contentMode === 'rss_with_ai' && (!data.rssFeeds || data.rssFeeds.length === 0)) {
      errors.push('RSS feed topic is required when using RSS with AI mode');
    }

    // Validate platforms
    if (!data.platforms || data.platforms.length === 0) {
      errors.push('At least one platform must be selected');
    }

    // Validate LinkedIn organization for LinkedIn platform
    if (data.platforms.includes('linkedin') && !data.selectedLinkedInOrganization) {
      errors.push('LinkedIn posting account must be selected when LinkedIn platform is enabled');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  return {
    submitPreferences,
    validatePreferences,
    loading
  };
}

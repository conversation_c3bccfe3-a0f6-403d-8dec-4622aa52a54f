import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { DayOfWeek, usePostPreferences, ContentMode } from "@/context/PostPreferencesContext"; //
import { Loader2, Save, Star, Edit, Rss } from "lucide-react";
import { LinkedInOrganizationSelector, LinkedInOrganization } from "./LinkedInOrganizationSelector";
import { useAuth } from "@/context/AuthContext"; //
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { SocialConnectors } from "./SocialConnectors";
import { useTranslation } from "@/hooks/useTranslation";
import { Badge } from "@/components/ui/badge";

export function PostingPreferences() {
  const { preferences, updatePreferences, savePreferences, loading } = usePostPreferences();
  const { user } = useAuth()
  const { t } = useTranslation();
  
  type SentimentType = "professional" | "optimistic" | "thoughtful" | "educational" | "inspirational" | "funny"; //
  
  const [localPrefs, setLocalPrefs] = useState({
    frequency: preferences.frequency.toString(),
    timeOfDay: preferences.timeOfDay,
    daysOfWeek: [...preferences.daysOfWeek],
    contentMode: preferences.contentMode,
    platforms: [...preferences.platforms],
    sentiment: preferences.sentiment as SentimentType,
    rssTopic: preferences.rssFeeds.length > 0 ? preferences.rssFeeds[0] : "",
    topics: [...preferences.topics],
    makeComWebhookUrl: preferences.makeComWebhookUrl || "",
  });

  const [topicInput, setTopicInput] = useState("");
  const [selectedLinkedInOrg, setSelectedLinkedInOrg] = useState<LinkedInOrganization | null>(null);

  const daysOfWeek: { id: DayOfWeek; label: string }[] = [
    { id: "monday", label: "Monday" },
    { id: "tuesday", label: "Tuesday" },
    { id: "wednesday", label: "Wednesday" },
    { id: "thursday", label: "Thursday" },
    { id: "friday", label: "Friday" },
    { id: "saturday", label: "Saturday" },
    { id: "sunday", label: "Sunday" },
  ];

  const sentimentOptions = [
    { value: "professional", label: t("preferences.professional") },
    { value: "optimistic", label: t("preferences.optimistic") },
    { value: "thoughtful", label: t("preferences.thoughtful") },
    { value: "educational", label: t("preferences.educational") },
    { value: "inspirational", label: t("preferences.inspirational") },
    { value: "funny", label: t("preferences.funny") },
  ];

  const handleDayToggle = (day: DayOfWeek) => {
    const updatedDays = localPrefs.daysOfWeek.includes(day)
      ? localPrefs.daysOfWeek.filter((d) => d !== day)
      : [...localPrefs.daysOfWeek, day];
    
    setLocalPrefs({
      ...localPrefs,
      daysOfWeek: updatedDays,
    });
  };

  const handleAddTopic = () => {
    if (topicInput.trim() && !localPrefs.topics.includes(topicInput.trim())) {
      setLocalPrefs({
        ...localPrefs,
        topics: [...localPrefs.topics, topicInput.trim()],
      });
      setTopicInput("");
    }
  };

  const handleRemoveTopic = (topic: string) => {
    setLocalPrefs({
      ...localPrefs,
      topics: localPrefs.topics.filter((t) => t !== topic),
    });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddTopic();
    }
  };

  const handleSave = async () => {
    const updatedPrefs = {
      frequency: parseInt(localPrefs.frequency),
      timeOfDay: localPrefs.timeOfDay,
      daysOfWeek: localPrefs.daysOfWeek,
      contentMode: localPrefs.contentMode as ContentMode,
      sentiment: localPrefs.sentiment as SentimentType,
      rssFeeds: localPrefs.rssTopic ? [localPrefs.rssTopic] : [],
      topics: localPrefs.topics,
      makeComWebhookUrl: localPrefs.makeComWebhookUrl,
    };
    
    updatePreferences(updatedPrefs);
    await savePreferences();
  };

  const RequiredFieldLabel = ({ children }: { children: React.ReactNode }) => (
    <div className="flex items-center gap-1">
      {children}
      <Star className="h-4 w-4 text-red-500" fill="currentColor" />
    </div>
  );

  const isSocialConnected = user?.linkedin_connected || false;

  return (
    <div className="space-y-8">
      <SocialConnectors />
      
      {!user?.linkedin_connected && (
        <div className="p-4 border rounded-lg bg-yellow-50">
          <p className="font-medium text-yellow-800 mb-3">
            Connect your LinkedIn account above to enable posting to LinkedIn
          </p>
        </div>
      )}
      
      <div className="p-4 border rounded-lg bg-blue-50">
        <p className="text-blue-800">
          <strong>Note:</strong> Fields marked with a <Star className="h-4 w-4 text-red-500 inline" fill="currentColor" /> are required.
        </p>
      </div>
      
      <div className="space-y-6">
        <div>
          <RequiredFieldLabel>
            <Label htmlFor="content-mode">Content Source</Label>
          </RequiredFieldLabel>
          <RadioGroup
            value={localPrefs.contentMode}
            onValueChange={(value) => 
              setLocalPrefs({ ...localPrefs, contentMode: value as ContentMode })
            }
            className="mt-2 space-y-3"
          >
            <div className="flex items-start space-x-2">
              <RadioGroupItem value="rss_with_ai" id="rss-with-ai" />
              <Label htmlFor="rss-with-ai" className="font-normal cursor-pointer">
                <div className="font-medium">RSS Feed with AI Enhancement</div>
                <p className="text-sm text-gray-500">
                  We'll monitor RSS feeds for content related to your topics, then use AI to create engaging posts with your preferred tone.
                </p>
              </Label>
            </div>
            <div className="flex items-start space-x-2">
              <RadioGroupItem value="ai_only" id="ai-only" />
              <Label htmlFor="ai-only" className="font-normal cursor-pointer">
                <div className="font-medium">AI-Generated Content Only</div>
                <p className="text-sm text-gray-500">
                  Our AI will create original content based on your topics and preferences without using RSS feeds.
                </p>
              </Label>
            </div>
          </RadioGroup>
        </div>

        {localPrefs.contentMode === "rss_with_ai" && (
          <div>
            <RequiredFieldLabel>
              <Label htmlFor="rss-topic" className="flex items-center">
                <Rss className="h-4 w-4 mr-1" /> RSS Feed Topic
              </Label>
            </RequiredFieldLabel>
            <Input
              id="rss-topic"
              placeholder="Enter a topic (e.g., Digital Marketing, AI Technology)"
              value={localPrefs.rssTopic}
              onChange={(e) => {
                const value = e.target.value.slice(0, 50); 
                setLocalPrefs({ ...localPrefs, rssTopic: value });
              }}
              className="w-full mt-1"
              maxLength={50}
            />
            <div className="flex justify-between mt-1">
              <p className="text-sm text-gray-500">
                Enter a topic that Make.com will use to create an RSS feed
              </p>
              <p className="text-sm text-gray-500">
                {localPrefs.rssTopic.length}/50
              </p>
            </div>
          </div>
        )}

        {isSocialConnected && (
          <div className="space-y-2">
            <LinkedInOrganizationSelector 
              onSelectionChange={setSelectedLinkedInOrg}
              required
            />
          </div>
        )}

        {/* Content Topics Section - Integrated directly here */}
        <div>
          <Label htmlFor="topics">Content Topics</Label>
          <div className="flex space-x-2 mt-1">
            <Input
              id="topics"
              value={topicInput}
              onChange={(e) => setTopicInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Add a topic (e.g., Marketing, Leadership)"
              className="flex-1"
            />
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={handleAddTopic}
              disabled={!topicInput.trim()}
            >
              <Edit className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="flex flex-wrap gap-2 mt-3">
            {localPrefs.topics.length > 0 ? (
              localPrefs.topics.map((topic) => (
                <Badge key={topic} variant="secondary" className="flex items-center gap-1 px-3 py-1">
                  {topic}
                  <button
                    type="button"
                    onClick={() => handleRemoveTopic(topic)}
                    className="hover:text-destructive focus:outline-none"
                  >
                    <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-3 w-3">
                      <path d="M12.8536 2.85355C13.0488 2.65829 13.0488 2.34171 12.8536 2.14645C12.6583 1.95118 12.3417 1.95118 12.1464 2.14645L7.5 6.79289L2.85355 2.14645C2.65829 1.95118 2.34171 1.95118 2.14645 2.14645C1.95118 2.34171 1.95118 2.65829 2.14645 2.85355L6.79289 7.5L2.14645 12.1464C1.95118 12.3417 1.95118 12.6583 2.14645 12.8536C2.34171 13.0488 2.65829 13.0488 2.85355 12.8536L7.5 8.20711L12.1464 12.8536C12.3417 13.0488 12.6583 13.0488 12.8536 12.8536C13.0488 12.6583 13.0488 12.3417 12.8536 12.1464L8.20711 7.5L12.8536 2.85355Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
                    </svg>
                  </button>
                </Badge>
              ))
            ) : (
              <p className="text-sm text-gray-500 mt-1">
                No topics added yet. Add topics to make your content more relevant.
              </p>
            )}
          </div>
        </div>

        {/* Content Sentiment/Tone - Integrated directly */}
        <div>
          <RequiredFieldLabel>
            <Label htmlFor="sentiment">Content Tone</Label>
          </RequiredFieldLabel>
          <Select
            value={localPrefs.sentiment}
            onValueChange={(value: SentimentType) => setLocalPrefs({ ...localPrefs, sentiment: value })}
          >
            <SelectTrigger id="sentiment" className="w-full mt-1">
              <SelectValue placeholder="Select content tone" />
            </SelectTrigger>
            <SelectContent>
              {sentimentOptions.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-sm text-gray-500 mt-1">
            The general tone and style for your generated content
          </p>
        </div>

        {/* Make.com Webhook URL */}
        <div>
          <Label htmlFor="webhook-url">Make.com Webhook URL</Label>
          <Input
            id="webhook-url"
            placeholder="Enter your Make.com workflow webhook URL"
            value={localPrefs.makeComWebhookUrl || ""}
            onChange={(e) => 
              setLocalPrefs({ ...localPrefs, makeComWebhookUrl: e.target.value })
            }
            className="w-full mt-1"
          />
          <p className="text-sm text-gray-500 mt-1">
            Enter the webhook URL from your Make.com workflow to enable automatic posting
          </p>
        </div>

        <div>
          <RequiredFieldLabel>
            <Label htmlFor="frequency">Posts per day</Label>
          </RequiredFieldLabel>
          <Select
            value={localPrefs.frequency}
            onValueChange={(value) => setLocalPrefs({ ...localPrefs, frequency: value })}
          >
            <SelectTrigger id="frequency" className="w-full mt-1">
              <SelectValue placeholder="Select frequency" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">1 post per day</SelectItem>
              <SelectItem value="2">2 posts per day</SelectItem>
              <SelectItem value="3">3 posts per day</SelectItem>
            </SelectContent>
          </Select>
          <p className="text-sm text-gray-500 mt-1">
            How many posts to publish each day (limited by your subscription plan)
          </p>
        </div>

        <div>
          <RequiredFieldLabel>
            <Label htmlFor="timeOfDay">Time of day</Label>
          </RequiredFieldLabel>
          <Input
            id="timeOfDay"
            type="time"
            value={localPrefs.timeOfDay}
            onChange={(e) => setLocalPrefs({ ...localPrefs, timeOfDay: e.target.value })}
            className="w-full mt-1"
          />
          <p className="text-sm text-gray-500 mt-1">
            When your posts will be published (in your local timezone)
          </p>
        </div>

        <div>
          <RequiredFieldLabel>
            <Label className="mb-2 block">Days of the week</Label>
          </RequiredFieldLabel>
          <div className="space-y-2 mt-1">
            {daysOfWeek.map((day) => (
              <div key={day.id} className="flex items-center space-x-2">
                <Checkbox
                  id={day.id}
                  checked={localPrefs.daysOfWeek.includes(day.id)}
                  onCheckedChange={() => handleDayToggle(day.id)}
                />
                <label
                  htmlFor={day.id}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {day.label}
                </label>
              </div>
            ))}
          </div>
          <p className="text-sm text-gray-500 mt-2">
            Select the days when you want to publish posts
          </p>
        </div>

        <div className="pt-4">
          <Button 
            onClick={handleSave} 
            disabled={loading || localPrefs.daysOfWeek.length === 0 || 
              (localPrefs.contentMode === "rss_with_ai" && !localPrefs.rssTopic)}
            className="w-full md:w-auto"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" /> Save Preferences
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}

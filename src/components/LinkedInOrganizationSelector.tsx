import { useState, useEffect } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Building2, User, Linkedin } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/hooks/use-toast";

export interface LinkedInOrganization {
  id: string;
  organization_id: string;
  organization_name: string;
  organization_type: 'person' | 'org';
  vanity_name: string | null;
  logo_url: string | null;
}

interface LinkedInOrganizationSelectorProps {
  onSelectionChange?: (selectedOrg: LinkedInOrganization | null) => void;
}

export function LinkedInOrganizationSelector({ onSelectionChange }: LinkedInOrganizationSelectorProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [organizations, setOrganizations] = useState<LinkedInOrganization[]>([]);
  const [selectedOrgId, setSelectedOrgId] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (user) {
      loadOrganizations();
    }
  }, [user]);

  const loadOrganizations = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('linkedin_organizations')
        .select('id, organization_id, organization_name, organization_type, vanity_name, logo_url')
        .eq('user_id', user.id)
        .order('organization_type', { ascending: false })
        .order('organization_name');

      if (error) {
        console.error('Error loading LinkedIn organizations:', error);
        toast({
          title: "Error",
          description: "Failed to load LinkedIn organizations.",
          variant: "destructive",
        });
        return;
      }

      if (data) {
        const transformedData: LinkedInOrganization[] = data.map(org => ({
          id: org.id,
          organization_id: org.organization_id,
          organization_name: org.organization_name,
          organization_type: org.organization_type as 'person' | 'org',
          vanity_name: org.vanity_name,
          logo_url: org.logo_url,
        }));
        setOrganizations(transformedData);
      } else {
        setOrganizations([]);
      }
    } catch (error) {
      console.error('Error loading organizations:', error);
      toast({
        title: "Error",
        description: "Failed to load LinkedIn organizations.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectionChange = (value: string) => {
    setSelectedOrgId(value);
    const selectedOrg = organizations.find(org => org.organization_id === value) || null;
    onSelectionChange?.(selectedOrg);
  };

  if (!user?.linkedin_connected || (organizations.length === 0 && !isLoading)) {
    return null;
  }

  return (
    <div className="space-y-2">
      <Label htmlFor="linkedin-org-select" className="flex items-center gap-2">
        <Linkedin className="h-4 w-4 text-blue-600" />
        Post as
      </Label>
      <Select value={selectedOrgId} onValueChange={handleSelectionChange}>
        <SelectTrigger id="linkedin-org-select" disabled={isLoading}>
          <SelectValue placeholder="Select an account to post as" />
        </SelectTrigger>
        <SelectContent className="bg-white">
          {organizations.map((org) => (
            <SelectItem key={org.organization_id} value={org.organization_id}>
              <div className="flex items-center gap-2">
                {org.organization_type === 'person' ? (
                  <User className="h-4 w-4 text-blue-600" />
                ) : (
                  <Building2 className="h-4 w-4 text-gray-600" />
                )}
                <span>{org.organization_name}</span>
                {org.organization_type === 'person' && (
                  <span className="text-xs text-gray-500">(Personal)</span>
                )}
                {org.vanity_name && (
                  <span className="text-xs text-gray-400">@{org.vanity_name}</span>
                )}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <p className="text-sm text-muted-foreground">
        Choose which LinkedIn account to post content as
      </p>
      {selectedOrgId && (
        <p className="text-sm text-gray-600">
          Posts will be published from the selected account
        </p>
      )}
    </div>
  );
}
